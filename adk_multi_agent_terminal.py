#!/usr/bin/env python3
"""
基于 ADK 的真正多智能体终端交互
使用 ADK 官方的多智能体架构
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_multi_agent_system import adk_coordinator, adk_tool_coordinator
from google.adk.agents.invocation_context import InvocationContext
from google.adk.sessions import Session


async def run_adk_multi_agent_chat():
    """运行基于 ADK 的多智能体聊天"""

    print("🚀 ADK 多智能体系统")
    print("=" * 60)
    print("🎯 基于 Google ADK 官方多智能体架构")
    print()
    print("🤖 系统架构:")
    print("   📋 MainCoordinator (主协调器)")
    print("      ├── 🐍 PythonSpecialist (Python专家)")
    print("      ├── 🎨 FrontendSpecialist (前端专家)")
    print("      ├── 🏗️ SystemArchitect (系统架构师)")
    print("      └── 🔄 FullStackWorkflow (全栈工作流)")
    print()
    print("💡 特色功能:")
    print("   • LLM驱动的智能代理转移")
    print("   • 层次化代理架构")
    print("   • 并行开发工作流")
    print("   • 共享会话状态")
    print()
    print("🎮 试试这些需求:")
    print("   • '创建一个用户管理系统' (全栈协作)")
    print("   • '写一个快速排序算法' (Python专家)")
    print("   • '设计一个登录界面' (前端专家)")
    print("   • '设计一个微服务架构' (系统架构师)")
    print()
    print("输入 'help' 查看帮助，'quit' 退出")
    print("-" * 60)

    # 创建会话
    session = Session(
        id="adk_multi_agent_session",
        appName="ADK Multi-Agent System",
        userId="user_001"
    )

    while True:
        try:
            user_input = input("\n🧑‍💻 您: ").strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 感谢使用 ADK 多智能体系统！")
                break

            if user_input.lower() in ['help', '帮助']:
                print_help()
                continue

            if user_input.lower() in ['status', '状态']:
                print_system_status()
                continue

            if not user_input:
                continue

            print(f"\n🤖 {adk_coordinator.name}: 正在分析需求并协调代理...")

            # 使用 ADK Runner 来运行代理
            try:
                print("🔄 启动 ADK 多智能体处理流程...")

                from google.adk import Runner
                from google.adk.sessions import InMemorySessionService

                # 创建会话服务
                session_service = InMemorySessionService()

                # 创建 Runner 并运行代理
                runner = Runner(
                    app_name="ADK Multi-Agent System",
                    agent=adk_coordinator,
                    session_service=session_service
                )

                # 运行代理并处理响应
                async for event in runner.run_async(user_input, session_id=session.id):
                    if event.content and event.content.parts:
                        for part in event.content.parts:
                            if hasattr(part, 'text') and part.text:
                                print(f"\n📤 {event.author}: {part.text}")
                            elif hasattr(part, 'data') and part.data:
                                print(f"\n📤 {event.author}: [数据响应]")

                    # 显示代理转移信息
                    if hasattr(event, 'actions') and event.actions:
                        if hasattr(event.actions, 'transfer_to_agent'):
                            target_agent = event.actions.transfer_to_agent
                            print(f"🔄 转移到代理: {target_agent}")

                print("\n✅ 任务完成!")

            except Exception as e:
                print(f"\n❌ ADK 处理过程中出现错误: {str(e)}")
                print("💡 这可能是因为 Gemini API 未配置，系统将使用 DeepSeek 后备方案")

                # 后备方案：使用简化的处理
                await fallback_processing(user_input)

        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 系统错误: {str(e)}")


async def fallback_processing(user_input):
    """后备处理方案"""
    print("\n🔄 使用后备处理方案...")

    # 简单的关键词分析
    keywords = user_input.lower()

    if any(word in keywords for word in ['系统', 'architecture', '架构', '设计']):
        print("🏗️ 检测到系统架构需求，启动架构设计流程...")
        print("📋 系统架构建议:")
        print("   1. 需求分析和技术选型")
        print("   2. 数据库设计")
        print("   3. API 接口设计")
        print("   4. 前端架构设计")
        print("   5. 部署和运维方案")

    elif any(word in keywords for word in ['python', '后端', 'api', '算法']):
        print("🐍 检测到 Python 开发需求...")
        from adk_agents.tools import generate_python_code
        try:
            result = await generate_python_code(user_input)
            if result['status'] == 'success':
                print("✅ Python 代码生成成功!")
                print(f"\n{result['code'][:500]}...")
        except Exception as e:
            print(f"❌ 代码生成失败: {str(e)}")

    elif any(word in keywords for word in ['前端', 'react', 'vue', 'ui', '界面']):
        print("🎨 检测到前端开发需求...")
        from adk_agents.tools import generate_frontend_code
        try:
            result = await generate_frontend_code(user_input)
            if result['status'] == 'success':
                print("✅ 前端代码生成成功!")
                print(f"\n{result['code'][:500]}...")
        except Exception as e:
            print(f"❌ 代码生成失败: {str(e)}")
    else:
        print("🤔 通用需求处理...")
        print("💡 建议明确指定需求类型以获得更好的处理效果")


def print_help():
    """打印帮助信息"""
    print("\n📖 ADK 多智能体系统帮助:")
    print("=" * 50)
    print("🎯 系统特色:")
    print("   • 基于 Google ADK 官方多智能体架构")
    print("   • 智能代理路由和任务分发")
    print("   • 层次化代理协作")
    print("   • 共享会话状态管理")
    print()
    print("🤖 可用代理:")
    print("   • MainCoordinator: 主协调器，智能分析和路由")
    print("   • PythonSpecialist: Python开发专家")
    print("   • FrontendSpecialist: 前端开发专家")
    print("   • SystemArchitect: 系统架构师")
    print("   • FullStackWorkflow: 全栈协作工作流")
    print()
    print("💬 命令:")
    print("   • help/帮助 - 显示此帮助")
    print("   • status/状态 - 显示系统状态")
    print("   • quit/exit/退出 - 退出系统")


def print_system_status():
    """打印系统状态"""
    print("\n📊 ADK 多智能体系统状态:")
    print("=" * 50)
    print(f"🤖 主协调器: {adk_coordinator.name}")
    print(f"📋 子代理数量: {len(adk_coordinator.sub_agents) if adk_coordinator.sub_agents else 0}")

    if adk_coordinator.sub_agents:
        print("🔗 代理层次结构:")
        for i, agent in enumerate(adk_coordinator.sub_agents, 1):
            print(f"   {i}. {agent.name} - {agent.description}")

    print(f"\n🛠️ 工具协调器: {adk_tool_coordinator.name}")
    print(f"🔧 可用工具: {len(adk_tool_coordinator.tools) if adk_tool_coordinator.tools else 0}")


def main():
    """主函数"""
    try:
        asyncio.run(run_adk_multi_agent_chat())
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")


if __name__ == "__main__":
    main()
