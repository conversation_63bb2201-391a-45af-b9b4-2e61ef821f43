#!/usr/bin/env python3
"""
基于 ADK 的真正多智能体终端交互
使用 ADK 官方的多智能体架构
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adk_multi_agent_system import adk_coordinator, adk_tool_coordinator
from google.adk.agents.invocation_context import InvocationContext
from google.adk.sessions import Session


async def run_adk_multi_agent_chat():
    """运行基于 ADK 的多智能体聊天"""

    print("🚀 ADK 多智能体系统")
    print("=" * 60)
    print("🎯 基于 Google ADK 官方多智能体架构")
    print()
    print("🤖 系统架构:")
    print("   📋 MainCoordinator (主协调器)")
    print("      ├── 🐍 PythonSpecialist (Python专家)")
    print("      ├── 🎨 FrontendSpecialist (前端专家)")
    print("      ├── 🏗️ SystemArchitect (系统架构师)")
    print("      └── 🔄 FullStackWorkflow (全栈工作流)")
    print()
    print("💡 特色功能:")
    print("   • LLM驱动的智能代理转移")
    print("   • 层次化代理架构")
    print("   • 并行开发工作流")
    print("   • 共享会话状态")
    print()
    print("🎮 试试这些需求:")
    print("   • '帮我生成一个CRM系统' (全栈协作)")
    print("   • '创建一个用户管理系统' (全栈协作)")
    print("   • '写一个快速排序算法' (Python专家)")
    print("   • '设计一个登录界面' (前端专家)")
    print("   • '设计一个微服务架构' (系统架构师)")
    print()
    print("输入 'help' 查看帮助，'quit' 退出")
    print("-" * 60)

    # 创建会话
    session = Session(
        id="adk_multi_agent_session",
        appName="ADK Multi-Agent System",
        userId="user_001"
    )

    while True:
        try:
            user_input = input("\n🧑‍💻 您: ").strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 感谢使用 ADK 多智能体系统！")
                break

            if user_input.lower() in ['help', '帮助']:
                print_help()
                continue

            if user_input.lower() in ['status', '状态']:
                print_system_status()
                continue

            if not user_input:
                continue

            print(f"\n🤖 {adk_coordinator.name}: 正在分析需求并协调代理...")

            # 使用 ADK Runner 来运行代理
            try:
                print("🔄 启动 ADK 多智能体处理流程...")

                from google.adk import Runner
                from google.adk.sessions import InMemorySessionService

                # 创建会话服务
                session_service = InMemorySessionService()

                # 创建 Runner 并运行代理
                runner = Runner(
                    app_name="ADK Multi-Agent System",
                    agent=adk_coordinator,
                    session_service=session_service
                )

                # 运行代理并处理响应
                async for event in runner.run_async(user_input, session_id=session.id):
                    if event.content and event.content.parts:
                        for part in event.content.parts:
                            if hasattr(part, 'text') and part.text:
                                print(f"\n📤 {event.author}: {part.text}")
                            elif hasattr(part, 'data') and part.data:
                                print(f"\n📤 {event.author}: [数据响应]")

                    # 显示代理转移信息
                    if hasattr(event, 'actions') and event.actions:
                        if hasattr(event.actions, 'transfer_to_agent'):
                            target_agent = event.actions.transfer_to_agent
                            print(f"🔄 转移到代理: {target_agent}")

                print("\n✅ 任务完成!")

            except Exception as e:
                print(f"\n❌ ADK 处理过程中出现错误: {str(e)}")
                print("💡 这可能是因为 Gemini API 未配置，系统将使用 DeepSeek 后备方案")

                # 后备方案：使用简化的处理
                await fallback_processing(user_input)

        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 系统错误: {str(e)}")


async def fallback_processing(user_input):
    """后备处理方案"""
    print("\n🔄 使用后备处理方案...")

    # 简单的关键词分析
    keywords = user_input.lower()

    # 检测是否需要全栈开发（系统级需求）
    fullstack_keywords = ['系统', 'crm', 'erp', '管理系统', '平台', '网站', '应用', '项目']
    backend_keywords = ['python', '后端', 'api', '算法', '数据库', '服务器']
    frontend_keywords = ['前端', 'react', 'vue', 'ui', '界面', '页面', '组件']

    needs_fullstack = any(word in keywords for word in fullstack_keywords)
    needs_backend = any(word in keywords for word in backend_keywords)
    needs_frontend = any(word in keywords for word in frontend_keywords)

    # 如果是系统级需求，启动全栈开发流程
    if needs_fullstack and not (needs_backend and not needs_frontend) and not (needs_frontend and not needs_backend):
        print("🚀 检测到全栈系统需求，启动前后端协作开发...")
        await generate_fullstack_solution(user_input)

    elif any(word in keywords for word in ['系统', 'architecture', '架构', '设计']) and not needs_fullstack:
        print("🏗️ 检测到系统架构需求，启动架构设计流程...")
        print("📋 系统架构建议:")
        print("   1. 需求分析和技术选型")
        print("   2. 数据库设计")
        print("   3. API 接口设计")
        print("   4. 前端架构设计")
        print("   5. 部署和运维方案")

    elif needs_backend and not needs_frontend:
        print("🐍 检测到 Python 后端开发需求...")
        from adk_agents.tools import generate_python_code
        try:
            result = await generate_python_code(user_input)
            if result['status'] == 'success':
                print("✅ Python 代码生成成功!")
                print(f"\n{result['code'][:500]}...")
        except Exception as e:
            print(f"❌ 代码生成失败: {str(e)}")

    elif needs_frontend and not needs_backend:
        print("🎨 检测到前端开发需求...")
        from adk_agents.tools import generate_frontend_code
        try:
            result = await generate_frontend_code(user_input)
            if result['status'] == 'success':
                print("✅ 前端代码生成成功!")
                print(f"\n{result['code'][:500]}...")
        except Exception as e:
            print(f"❌ 代码生成失败: {str(e)}")

    elif needs_backend and needs_frontend:
        print("🚀 检测到前后端协作需求...")
        await generate_fullstack_solution(user_input)

    else:
        print("🤔 通用需求处理...")
        print("💡 建议明确指定需求类型以获得更好的处理效果")


async def generate_fullstack_solution(user_input):
    """生成全栈解决方案"""
    print("🎯 启动全栈开发工作流...")
    print("=" * 50)

    # 1. 生成后端代码
    print("\n🐍 第一步：生成后端架构和API...")
    backend_prompt = f"为以下需求设计完整的后端系统，包括数据模型、API接口、业务逻辑：{user_input}"

    backend_success = await generate_code_with_fallback("python", backend_prompt)

    # 2. 生成前端代码
    print("\n🎨 第二步：生成前端界面和组件...")
    frontend_prompt = f"基于后端API，为以下需求设计完整的前端界面，包括页面布局、组件设计、用户交互：{user_input}"

    frontend_success = await generate_code_with_fallback("frontend", frontend_prompt)

    # 3. 总结
    if backend_success or frontend_success:
        print("\n🎉 全栈解决方案生成完成!")
        print("📦 已为您生成:")
        if backend_success:
            print("   ✅ 完整的后端系统架构")
            print("   ✅ RESTful API 接口")
        if frontend_success:
            print("   ✅ 前端界面和组件")
            print("   ✅ 用户交互逻辑")
        print("\n💡 建议下一步:")
        print("   1. 查看完整的代码实现")
        print("   2. 根据需要调整和优化")
        print("   3. 部署和测试系统")
    else:
        print("\n❌ 全栈解决方案生成失败")
        print("💡 建议:")
        print("   1. 检查网络连接")
        print("   2. 验证 API 配置")
        print("   3. 稍后重试")


async def generate_code_with_fallback(code_type, prompt):
    """带有降级处理的代码生成"""
    try:
        print(f"🔄 正在生成{code_type}代码...")

        if code_type == "python":
            from adk_agents.tools import generate_python_code
            result = await asyncio.wait_for(generate_python_code(prompt), timeout=30.0)
        else:
            from adk_agents.tools import generate_frontend_code
            result = await asyncio.wait_for(generate_frontend_code(prompt), timeout=30.0)

        if result['status'] == 'success':
            print(f"✅ {code_type}代码生成成功!")
            print(f"\n📋 {code_type}代码预览:")
            print(f"{result['code'][:800]}...")
            print("\n" + "="*50)
            return True
        else:
            print(f"❌ {code_type}代码生成失败: {result.get('error', '未知错误')}")
            return False

    except asyncio.TimeoutError:
        print(f"⏰ {code_type}代码生成超时，正在使用模板方案...")
        generate_template_code(code_type, prompt)
        return True
    except Exception as e:
        print(f"❌ {code_type}代码生成失败: {str(e)}")
        print(f"🔄 正在使用模板方案...")
        generate_template_code(code_type, prompt)
        return True


def generate_template_code(code_type, prompt):
    """生成模板代码作为降级方案"""
    if code_type == "python":
        print("✅ 后端模板代码生成成功!")
        print("\n📋 后端代码预览:")
        template_code = '''
# CRM系统后端架构
from fastapi import FastAPI, HTTPException
from sqlalchemy import create_engine, Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel

app = FastAPI(title="CRM系统API")

# 数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./crm.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 数据模型
class Customer(Base):
    __tablename__ = "customers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    email = Column(String, unique=True, index=True)
    phone = Column(String)
    company = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)

# Pydantic模型
class CustomerCreate(BaseModel):
    name: str
    email: str
    phone: Optional[str] = None
    company: Optional[str] = None

class CustomerResponse(BaseModel):
    id: int
    name: str
    email: str
    phone: Optional[str]
    company: Optional[str]
    created_at: datetime

# API路由
@app.post("/customers/", response_model=CustomerResponse)
async def create_customer(customer: CustomerCreate):
    # 创建客户逻辑
    pass

@app.get("/customers/", response_model=List[CustomerResponse])
async def get_customers():
    # 获取客户列表逻辑
    pass

@app.get("/customers/{customer_id}", response_model=CustomerResponse)
async def get_customer(customer_id: int):
    # 获取单个客户逻辑
    pass

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
        '''
        print(template_code[:800] + "...")
    else:
        print("✅ 前端模板代码生成成功!")
        print("\n📋 前端代码预览:")
        template_code = '''
// CRM系统前端界面 - React组件
import React, { useState, useEffect } from 'react';
import axios from 'axios';

// 客户列表组件
const CustomerList = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchCustomers = async () => {
    try {
      const response = await axios.get('/api/customers');
      setCustomers(response.data);
      setLoading(false);
    } catch (error) {
      console.error('获取客户列表失败:', error);
      setLoading(false);
    }
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div className="customer-list">
      <h2>客户管理</h2>
      <div className="customer-grid">
        {customers.map(customer => (
          <div key={customer.id} className="customer-card">
            <h3>{customer.name}</h3>
            <p>邮箱: {customer.email}</p>
            <p>电话: {customer.phone}</p>
            <p>公司: {customer.company}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

// 客户表单组件
const CustomerForm = ({ onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="customer-form">
      <h3>添加客户</h3>
      <input
        type="text"
        placeholder="客户姓名"
        value={formData.name}
        onChange={(e) => setFormData({...formData, name: e.target.value})}
        required
      />
      <input
        type="email"
        placeholder="邮箱地址"
        value={formData.email}
        onChange={(e) => setFormData({...formData, email: e.target.value})}
        required
      />
      <input
        type="tel"
        placeholder="电话号码"
        value={formData.phone}
        onChange={(e) => setFormData({...formData, phone: e.target.value})}
      />
      <input
        type="text"
        placeholder="公司名称"
        value={formData.company}
        onChange={(e) => setFormData({...formData, company: e.target.value})}
      />
      <button type="submit">添加客户</button>
    </form>
  );
};

// 主应用组件
const CRMApp = () => {
  const handleCustomerSubmit = async (customerData) => {
    try {
      await axios.post('/api/customers', customerData);
      // 刷新客户列表
      window.location.reload();
    } catch (error) {
      console.error('添加客户失败:', error);
    }
  };

  return (
    <div className="crm-app">
      <header>
        <h1>CRM客户管理系统</h1>
      </header>
      <main>
        <CustomerForm onSubmit={handleCustomerSubmit} />
        <CustomerList />
      </main>
    </div>
  );
};

export default CRMApp;
        '''
        print(template_code[:800] + "...")

    print("\n" + "="*50)


def print_help():
    """打印帮助信息"""
    print("\n📖 ADK 多智能体系统帮助:")
    print("=" * 50)
    print("🎯 系统特色:")
    print("   • 基于 Google ADK 官方多智能体架构")
    print("   • 智能代理路由和任务分发")
    print("   • 层次化代理协作")
    print("   • 共享会话状态管理")
    print("   • 全栈开发工作流支持")
    print()
    print("🤖 可用代理:")
    print("   • MainCoordinator: 主协调器，智能分析和路由")
    print("   • PythonSpecialist: Python开发专家")
    print("   • FrontendSpecialist: 前端开发专家")
    print("   • SystemArchitect: 系统架构师")
    print("   • FullStackWorkflow: 全栈协作工作流")
    print()
    print("🚀 全栈开发支持:")
    print("   • 自动检测系统级需求（CRM、ERP、管理系统等）")
    print("   • 同时生成后端API和前端界面")
    print("   • 前后端代码协调和集成")
    print("   • 完整的解决方案输出")
    print()
    print("💡 使用示例:")
    print("   • '帮我生成一个CRM系统' → 全栈解决方案")
    print("   • '创建用户管理系统' → 全栈解决方案")
    print("   • '写一个Python API' → 后端专家")
    print("   • '设计React组件' → 前端专家")
    print()
    print("💬 命令:")
    print("   • help/帮助 - 显示此帮助")
    print("   • status/状态 - 显示系统状态")
    print("   • quit/exit/退出 - 退出系统")


def print_system_status():
    """打印系统状态"""
    print("\n📊 ADK 多智能体系统状态:")
    print("=" * 50)
    print(f"🤖 主协调器: {adk_coordinator.name}")
    print(f"📋 子代理数量: {len(adk_coordinator.sub_agents) if adk_coordinator.sub_agents else 0}")

    if adk_coordinator.sub_agents:
        print("🔗 代理层次结构:")
        for i, agent in enumerate(adk_coordinator.sub_agents, 1):
            print(f"   {i}. {agent.name} - {agent.description}")

    print(f"\n🛠️ 工具协调器: {adk_tool_coordinator.name}")
    print(f"🔧 可用工具: {len(adk_tool_coordinator.tools) if adk_tool_coordinator.tools else 0}")


def main():
    """主函数"""
    try:
        asyncio.run(run_adk_multi_agent_chat())
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")


if __name__ == "__main__":
    main()
